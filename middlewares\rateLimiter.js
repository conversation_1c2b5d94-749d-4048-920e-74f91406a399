const rateLimit = require('express-rate-limit');
const axios = require('axios');
const config = require('../config/config');
const tykConfig = require('../config/tyk');
const logger = require('../config/logger');

/**
 * Tyk Rate Limiting Middleware
 * Checks rate limits with Tyk Gateway when in Tyk mode
 */
const tykRateLimiter = async (req, res, next) => {
  try {
    // Extract API key or identity from request
    const apiKey = req.headers['x-api-key'] || req.headers.authorization?.replace('Bearer ', '');
    const identity = req.identity?.identity_id || 'anonymous';

    if (!apiKey) {
      // No API key, let it pass to authentication middleware
      return next();
    }

    // Check rate limit with Tyk Gateway
    const response = await axios.get(
      `${tykConfig.gatewayUrl}/tyk/rate-limit-status`,
      {
        headers: {
          ...tykConfig.getGatewayHeaders(),
          'X-API-Key': apiKey
        },
        timeout: 5000
      }
    );

    const rateLimitData = response.data;

    // Add rate limit headers to response
    res.set({
      'X-RateLimit-Limit': rateLimitData.quota_max || 1000,
      'X-RateLimit-Remaining': rateLimitData.quota_remaining || 0,
      'X-RateLimit-Reset': rateLimitData.quota_renews || Date.now() + 3600000
    });

    // Check if rate limit exceeded
    if (rateLimitData.quota_remaining <= 0) {
      logger.warn(`Rate limit exceeded for identity: ${identity}`);
      return res.status(429).json({
        status: false,
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: rateLimitData.quota_renews
      });
    }

    logger.debug(`Rate limit check passed for identity: ${identity}, remaining: ${rateLimitData.quota_remaining}`);
    next();

  } catch (error) {
    logger.error('Tyk rate limit check failed:', error.message);
    // Fallback to custom rate limiting if Tyk check fails
    return customApiLimiter(req, res, next);
  }
};

/**
 * Custom Rate Limiting (fallback)
 */
const customApiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,  // 15 minutes
  max: 10000,  // limit each IP to 10000 requests per window
  message: {
    status: false,
    message: 'Too many requests from this IP, please try again later.',
    retryAfter: 15 * 60 * 1000
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req) => {
    // Use identity ID if available, otherwise fall back to IP
    return req.identity?.identity_id || req.ip;
  },
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  },
  // onLimitReached has been removed in v7 - logging is now handled in the handler
  handler: (req, res, next, options) => {
    const identity = req.identity?.identity_id || 'anonymous';
    logger.warn(`Custom rate limit exceeded for identity: ${identity}, IP: ${req.ip}`);

    // Send the rate limit response
    res.status(options.statusCode).json(options.message);
  }
});

/**
 * Main rate limiter that chooses between Tyk and custom based on auth mode
 */
const apiLimiter = (req, res, next) => {
  if (config.auth.mode === 'tyk' && tykConfig.isEnabled) {
    logger.debug('Using Tyk rate limiting');
    return tykRateLimiter(req, res, next);
  } else {
    logger.debug('Using custom rate limiting');
    return customApiLimiter(req, res, next);
  }
};

module.exports = apiLimiter;

