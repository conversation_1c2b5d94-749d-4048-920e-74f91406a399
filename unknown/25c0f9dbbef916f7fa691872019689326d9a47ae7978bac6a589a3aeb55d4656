"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Fetch some device and nda IDs for association
    const devices = await queryInterface.sequelize.query(
      "SELECT device_id FROM device LIMIT 5;",
      { type: Sequelize.QueryTypes.SELECT }
    );
    const templates = await queryInterface.sequelize.query(
      "SELECT nda_template_id FROM nda_template LIMIT 5;",
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Prepare device settings
    const deviceSettings = [];
    for (let i = 0; i < Math.min(devices.length, templates.length); i++) {
      deviceSettings.push({
        device_setting_id: uuidv4(),
        device_id: devices[i].device_id,
        nda_template_id: templates[i].nda_template_id,
        shownda: i % 2 === 0,
        showoutpatient: i % 2 !== 0,
        showexpeditecheckin: true,
        showwalkinguest: false,
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    if (deviceSettings.length > 0) {
      await queryInterface.bulkInsert("device_setting", deviceSettings, {});
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete("device_setting", null, {});
  },
};
